{"name": "slurp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "testnet": "node testnet.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@meteora-ag/cp-amm-sdk": "^1.0.6", "@meteora-ag/dynamic-bonding-curve-sdk": "^1.1.9", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "bs58": "^6.0.0", "dotenv": "^16.5.0"}}