// Transaction batching service for efficient instruction execution
const { Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');

/**
 * Transaction Batcher class for managing and executing batched instructions
 */
class TransactionBatcher {
  constructor(connection, wallet, options = {}) {
    this.connection = connection;
    this.wallet = wallet;
    this.instructions = [];
    this.maxInstructionsPerTx = options.maxInstructionsPerTx || 20; // Conservative limit
    this.maxTransactionSize = options.maxTransactionSize || 1232; // Solana transaction size limit
    this.confirmationOptions = options.confirmationOptions || { commitment: 'confirmed' };
  }

  /**
   * Add instructions to the batch
   * @param {Array} instructions - Array of transaction instructions
   * @param {string} description - Optional description for logging
   */
  addInstructions(instructions, description = '') {
    if (!Array.isArray(instructions)) {
      instructions = [instructions];
    }
    
    this.instructions.push(...instructions);
    console.log(`Added ${instructions.length} instructions to batch${description ? ` (${description})` : ''}`);
    console.log(`Total instructions in batch: ${this.instructions.length}`);
  }

  /**
   * Clear all instructions from the batch
   */
  clear() {
    this.instructions = [];
    console.log('Transaction batch cleared');
  }

  /**
   * Get the current number of instructions in the batch
   * @returns {number} Number of instructions
   */
  getInstructionCount() {
    return this.instructions.length;
  }

  /**
   * Split instructions into multiple transactions based on size limits
   * @returns {Array<Array>} Array of instruction arrays for each transaction
   */
  splitIntoTransactions() {
    const transactions = [];
    let currentBatch = [];
    
    for (const instruction of this.instructions) {
      // Check if adding this instruction would exceed limits
      if (currentBatch.length >= this.maxInstructionsPerTx) {
        // Start a new transaction
        if (currentBatch.length > 0) {
          transactions.push([...currentBatch]);
          currentBatch = [];
        }
      }
      
      currentBatch.push(instruction);
    }
    
    // Add the last batch if it has instructions
    if (currentBatch.length > 0) {
      transactions.push(currentBatch);
    }
    
    console.log(`Split ${this.instructions.length} instructions into ${transactions.length} transactions`);
    return transactions;
  }

  /**
   * Execute all batched instructions as optimized transactions
   * @returns {Promise<Array<string>>} Array of transaction hashes
   */
  async execute() {
    if (this.instructions.length === 0) {
      console.log('No instructions to execute');
      return [];
    }

    console.log(`Executing batch of ${this.instructions.length} instructions...`);
    
    const transactionBatches = this.splitIntoTransactions();
    const txHashes = [];
    
    for (let i = 0; i < transactionBatches.length; i++) {
      const batch = transactionBatches[i];
      console.log(`Executing transaction ${i + 1}/${transactionBatches.length} with ${batch.length} instructions...`);
      
      try {
        const tx = new Transaction();
        tx.add(...batch);
        
        // Get recent blockhash
        const blockhash = await this.connection.getLatestBlockhash();
        tx.recentBlockhash = blockhash.blockhash;
        tx.feePayer = this.wallet.publicKey;
        
        // Sign the transaction
        tx.sign(this.wallet);
        
        // Send and confirm the transaction
        const txHash = await sendAndConfirmTransaction(
          this.connection, 
          tx, 
          [this.wallet],
          this.confirmationOptions
        );
        
        txHashes.push(txHash);
        console.log(`Transaction ${i + 1} confirmed: ${txHash}`);
        
        // Small delay between transactions to avoid rate limiting
        if (i < transactionBatches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        console.error(`Error executing transaction ${i + 1}:`, error);
        throw new Error(`Transaction batch ${i + 1} failed: ${error.message}`);
      }
    }
    
    console.log(`Successfully executed ${transactionBatches.length} transactions`);
    this.clear(); // Clear instructions after successful execution
    
    return txHashes;
  }

  /**
   * Estimate the total number of transactions that will be created
   * @returns {number} Estimated number of transactions
   */
  estimateTransactionCount() {
    if (this.instructions.length === 0) return 0;
    return Math.ceil(this.instructions.length / this.maxInstructionsPerTx);
  }

  /**
   * Get a summary of the current batch
   * @returns {Object} Batch summary
   */
  getSummary() {
    return {
      totalInstructions: this.instructions.length,
      estimatedTransactions: this.estimateTransactionCount(),
      maxInstructionsPerTx: this.maxInstructionsPerTx
    };
  }
}

module.exports = {
  TransactionBatcher
};
