// Balance utilities for SOL and tokens
const { PublicKey } = require('@solana/web3.js');

/**
 * Gets the SOL balance for a wallet
 * @param {Connection} connection - Solana connection
 * @param {PublicKey} publicKey - Wallet public key
 * @returns {Promise<number>} SOL balance in lamports
 */
async function getSolBalance(connection, publicKey) {
  const balance = await connection.getBalance(publicKey);
  console.log(`SOL Balance: ${balance} lamports (${balance / 1e9} SOL)`);
  return balance;
}

/**
 * Gets the token balance for a wallet
 * @param {Connection} connection - Solana connection
 * @param {PublicKey} walletPublicKey - Wallet public key
 * @param {PublicKey} tokenMintPublicKey - Token mint public key
 * @returns {Promise<number>} Token balance in token lamports
 */
async function getTokenBalance(connection, walletPublicKey, tokenMintPublicKey) {
  // This function would need to be implemented with the appropriate token program calls
  // For now, it's a placeholder
  console.log(`Getting token balance for ${tokenMintPublicKey.toString()}...`);
  // Implementation would go here
  return 0;
}

module.exports = {
  getSolBalance,
  getTokenBalance
};
