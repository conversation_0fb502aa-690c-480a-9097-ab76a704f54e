// Pool management utilities for multi-pool operations
const { getUnClaimReward } = require('@meteora-ag/cp-amm-sdk');
const { createPublicKey } = require('../utils/wallet');

/**
 * Initialize pools with their states and unclaimed rewards
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {Array} poolConfigs - Array of pool configurations from pools.json
 * @returns {Promise<Array>} Array of initialized pools with states
 */
async function initializePools(cpAmmSdk, poolConfigs) {
  console.log(`Initializing ${poolConfigs.length} pools...`);
  
  const initializedPools = [];
  
  for (const poolConfig of poolConfigs) {
    try {
      console.log(`\n--- Initializing ${poolConfig.name} ---`);
      
      // Convert string addresses to PublicKey objects
      const poolPubkey = createPublicKey(poolConfig.poolAddress);
      const positionPubkey = createPublicKey(poolConfig.positionAddress);
      const positionNftPubkey = createPublicKey(poolConfig.positionNftAddress);
      const positionNftTokenAccountPubkey = createPublicKey(poolConfig.positionNftTokenAccount);
      
      // Fetch pool state
      console.log(`Fetching pool state for ${poolConfig.name}...`);
      const poolState = await cpAmmSdk.fetchPoolState(poolPubkey);
      
      // Fetch position state
      console.log(`Fetching position state for ${poolConfig.name}...`);
      const positionState = await cpAmmSdk.fetchPositionState(positionPubkey);
      
      // Get unclaimed rewards
      console.log(`Checking unclaimed rewards for ${poolConfig.name}...`);
      const unclaimedReward = getUnClaimReward(poolState, positionState);
      
      const initializedPool = {
        ...poolConfig,
        poolPubkey,
        positionPubkey,
        positionNftPubkey,
        positionNftTokenAccountPubkey,
        poolState,
        positionState,
        unclaimedReward
      };
      
      initializedPools.push(initializedPool);
      
      console.log(`✓ ${poolConfig.name} initialized successfully`);
      console.log(`  Pool: ${poolPubkey.toString()}`);
      console.log(`  Position: ${positionPubkey.toString()}`);
      console.log(`  Token A mint: ${poolState.tokenAMint.toString()}`);
      console.log(`  Token B mint: ${poolState.tokenBMint.toString()}`);
      console.log(`  Unclaimed Token A: ${unclaimedReward.feeTokenA.toString()}`);
      console.log(`  Unclaimed Token B: ${unclaimedReward.feeTokenB.toString()}`);
      
    } catch (error) {
      console.error(`Error initializing pool ${poolConfig.name}:`, error);
      throw new Error(`Failed to initialize pool ${poolConfig.name}: ${error.message}`);
    }
  }
  
  console.log(`\n✓ Successfully initialized ${initializedPools.length} pools`);
  return initializedPools;
}

/**
 * Calculate total quote token fees across all pools
 * @param {Array} pools - Array of initialized pools
 * @returns {Object} Total fees summary
 */
function calculateTotalFees(pools) {
  console.log('\n--- Calculating Total Fees ---');
  
  let totalTokenAFees = 0;
  let totalTokenBFees = 0;
  let poolsWithFees = 0;
  
  for (const pool of pools) {
    const tokenAFees = pool.unclaimedReward.feeTokenA.toNumber();
    const tokenBFees = pool.unclaimedReward.feeTokenB.toNumber();
    
    if (tokenAFees > 0 || tokenBFees > 0) {
      poolsWithFees++;
      console.log(`${pool.name}:`);
      console.log(`  Token A fees: ${tokenAFees} (${tokenAFees / 1e9} in human units)`);
      console.log(`  Token B fees: ${tokenBFees} (${tokenBFees / 1e9} in human units)`);
    }
    
    totalTokenAFees += tokenAFees;
    totalTokenBFees += tokenBFees;
  }
  
  const summary = {
    totalTokenAFees,
    totalTokenBFees,
    poolsWithFees,
    totalPools: pools.length
  };
  
  console.log(`\nTotal Fees Summary:`);
  console.log(`  Total Token A fees: ${totalTokenAFees} (${totalTokenAFees / 1e9} in human units)`);
  console.log(`  Total Token B fees: ${totalTokenBFees} (${totalTokenBFees / 1e9} in human units)`);
  console.log(`  Pools with fees: ${poolsWithFees}/${pools.length}`);
  
  return summary;
}

/**
 * Filter pools that have claimable fees
 * @param {Array} pools - Array of initialized pools
 * @returns {Array} Pools with claimable fees
 */
function getPoolsWithClaimableFees(pools) {
  const poolsWithFees = pools.filter(pool => {
    const hasTokenAFees = pool.unclaimedReward.feeTokenA.gt(0);
    const hasTokenBFees = pool.unclaimedReward.feeTokenB.gt(0);
    return hasTokenAFees || hasTokenBFees;
  });
  
  console.log(`Found ${poolsWithFees.length}/${pools.length} pools with claimable fees`);
  return poolsWithFees;
}

/**
 * Validate pool configurations
 * @param {Array} poolConfigs - Array of pool configurations
 * @returns {boolean} True if all configurations are valid
 */
function validatePoolConfigs(poolConfigs) {
  console.log('Validating pool configurations...');
  
  if (!Array.isArray(poolConfigs) || poolConfigs.length === 0) {
    throw new Error('Pool configurations must be a non-empty array');
  }
  
  const requiredFields = ['name', 'ratio', 'poolAddress', 'positionAddress', 'positionNftAddress', 'positionNftTokenAccount'];
  
  for (let i = 0; i < poolConfigs.length; i++) {
    const pool = poolConfigs[i];
    
    for (const field of requiredFields) {
      if (!pool[field]) {
        throw new Error(`Pool ${i + 1} (${pool.name || 'unnamed'}) is missing required field: ${field}`);
      }
    }
    
    if (typeof pool.ratio !== 'number' || pool.ratio <= 0) {
      throw new Error(`Pool ${pool.name} has invalid ratio: ${pool.ratio}`);
    }
    
    // Check for placeholder addresses
    const placeholderFields = ['poolAddress', 'positionAddress', 'positionNftAddress', 'positionNftTokenAccount'];
    for (const field of placeholderFields) {
      if (pool[field].includes('REPLACE_WITH_ACTUAL')) {
        throw new Error(`Pool ${pool.name} has placeholder value for ${field}. Please update pools.json with actual addresses.`);
      }
    }
  }
  
  console.log(`✓ All ${poolConfigs.length} pool configurations are valid`);
  return true;
}

module.exports = {
  initializePools,
  calculateTotalFees,
  getPoolsWithClaimableFees,
  validatePoolConfigs
};
