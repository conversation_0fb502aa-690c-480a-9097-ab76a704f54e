// Wallet utilities
const { Keypair, PublicKey } = require('@solana/web3.js');
const bs58 = require('bs58');
const config = require('../config');

/**
 * Creates a keypair from the configured private key
 * @returns {Keypair} Solana keypair
 */
function createWallet() {
  // Decode the base58 private key string to a Uint8Array
  const secretKeyUint8Array = bs58.default.decode(config.PRIVATE_KEY);
  const keyPair = Keypair.fromSecretKey(secretKeyUint8Array);
  console.log(`Wallet public key: ${keyPair.publicKey.toString()}`);
  return keyPair;
}

/**
 * Creates a PublicKey object from a string address
 * @param {string} address - The public key address as a string
 * @returns {PublicKey} Solana PublicKey object
 */
function createPublicKey(address) {
  return new PublicKey(address);
}

module.exports = {
  createWallet,
  createPublicKey
};
