// Solana connection utilities
const { Connection } = require('@solana/web3.js');
const config = require('../config');

/**
 * Creates a Solana connection with the configured RPC endpoint
 * @returns {Connection} Solana connection object
 */
function createConnection() {
  console.log(`Initializing connection with URL ${config.RPC_ENDPOINT}...`);
  return new Connection(
    config.RPC_ENDPOINT, 
    { commitment: 'confirmed' }
  );
}

module.exports = {
  createConnection
};
