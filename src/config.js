// Configuration for Meteora CP-AMM interaction
require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Load pools configuration
let pools = [];
try {
  const poolsPath = path.join(__dirname, 'pools.json');
  const poolsData = fs.readFileSync(poolsPath, 'utf8');
  pools = JSON.parse(poolsData);
  console.log(`Loaded ${pools.length} pools from pools.json`);
} catch (error) {
  console.warn('Warning: Could not load pools.json, using empty pools array:', error.message);
}

// Environment variables with defaults
const config = {
  PRIVATE_KEY: process.env.PRIVATE_KEY,
  RPC_ENDPOINT: process.env.RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com',
  SLIPPAGE: process.env.SLIPPAGE ? parseFloat(process.env.SLIPPAGE) : 0.01, // 1% slippage default
  pools: pools
};

module.exports = config;
