// Main application logic using Meteora CP-AMM SDK for multi-pool operations
const { CpAmm } = require('@meteora-ag/cp-amm-sdk');
const { BN } = require('@coral-xyz/anchor');

// Import configuration
const config = require('./config');

// Import utilities
const { createConnection } = require('./utils/connection');
const { createWallet } = require('./utils/wallet');

// Import services
const { getSolBalance } = require('./services/balances');
const { createMultiPoolSwapInstructions } = require('./services/swap');
const { createMultiPoolLiquidityInstructions } = require('./services/liquidity');
const { createMultiPoolFeeClaimInstructions } = require('./services/vault');
const { TransactionBatcher } = require('./services/transactionBatcher');
const {
  initializePools,
  calculateTotalFees,
  getPoolsWithClaimableFees,
  validatePoolConfigs
} = require('./services/poolManager');

/**
 * Main function that orchestrates the multi-pool liquidity flywheel using Meteora CP-AMM
 */
async function main() {
  try {
    console.log('🚀 Starting Multi-Pool Liquidity Flywheel...\n');

    // Step 1: Validate configuration and initialize components
    console.log('=== STEP 1: INITIALIZATION ===');

    // Validate pool configurations
    validatePoolConfigs(config.pools);

    // Create Solana connection
    const solConnection = createConnection();
    console.log('✓ Solana connection established');

    // Create wallet from private key
    const wallet = createWallet();
    console.log(`✓ Wallet loaded: ${wallet.publicKey.toString()}`);

    // Initialize the Meteora CP-AMM SDK
    const cpAmmSdk = new CpAmm(solConnection);
    console.log('✓ Meteora CP-AMM SDK initialized');

    // Initialize transaction batcher
    const batcher = new TransactionBatcher(solConnection, wallet);
    console.log('✓ Transaction batcher initialized');

    // Check initial SOL balance
    const initialSolBalance = await getSolBalance(solConnection, wallet.publicKey);
    console.log(`✓ Initial SOL balance: ${initialSolBalance / 1e9} SOL`);

    // Step 2: Initialize all pools with their states
    console.log('\n=== STEP 2: POOL INITIALIZATION ===');
    const pools = await initializePools(cpAmmSdk, config.pools);

    // Step 3: Calculate total fees and check if we should proceed
    console.log('\n=== STEP 3: FEE ANALYSIS ===');
    calculateTotalFees(pools);
    const poolsWithFees = getPoolsWithClaimableFees(pools);

    if (poolsWithFees.length === 0) {
      console.log('❌ No pools have claimable fees. Exiting...');
      return;
    }

    // Step 4: Claim fees from all pools
    console.log('\n=== STEP 4: CLAIMING FEES ===');
    const feeClaimResults = await createMultiPoolFeeClaimInstructions(cpAmmSdk, poolsWithFees, wallet.publicKey);

    // Add fee claim instructions to batcher
    for (const result of feeClaimResults) {
      batcher.addInstructions(result.instructions, `Fee claim for ${result.pool.name}`);
    }

    // Calculate total quote token fees that will be claimed
    let totalQuoteTokenFees = new BN(0);
    for (const pool of poolsWithFees) {
      // Assuming tokenB is the quote token (SOL/USDC/etc) in most pools
      totalQuoteTokenFees = totalQuoteTokenFees.add(pool.unclaimedReward.feeTokenB);
    }

    console.log(`Total quote token fees to be claimed: ${totalQuoteTokenFees.toString()} (${totalQuoteTokenFees.toNumber() / 1e9} in human units)`);

    // Step 5: Check balance requirements
    console.log('\n=== STEP 5: BALANCE VALIDATION ===');
    const MIN_BALANCE_LAMPORTS = 0.15 * 1e9; // 0.15 SOL in lamports

    // Calculate updated balance after claiming fees
    let updatedSolBalance = initialSolBalance;
    for (const pool of poolsWithFees) {
      // Add tokenB fees (assuming tokenB is SOL in most pools)
      updatedSolBalance += pool.unclaimedReward.feeTokenB.toNumber();
    }

    console.log(`Updated SOL balance after claiming fees: ${updatedSolBalance / 1e9} SOL`);

    if (updatedSolBalance < MIN_BALANCE_LAMPORTS) {
      console.log(`❌ Not enough balance to continue. Current balance: ${updatedSolBalance / 1e9} SOL, minimum required: 0.15 SOL. Exiting...`);
      return;
    }

    // Step 6: Create swap instructions for all pools
    console.log('\n=== STEP 6: MULTI-POOL SWAPS ===');

    // Calculate half of total quote token fees for swapping
    const halfQuoteTokenFees = totalQuoteTokenFees.div(new BN(2));
    console.log(`Using ${halfQuoteTokenFees.toString()} (${halfQuoteTokenFees.toNumber() / 1e9} in human units) for swaps across all pools`);

    if (halfQuoteTokenFees.isZero()) {
      console.log('❌ No quote token fees to swap. Exiting...');
      return;
    }

    // Get current slot for quote calculations
    const currentSlot = await solConnection.getSlot({ commitment: 'confirmed' });

    // Create swap instructions for all pools
    const swapResults = await createMultiPoolSwapInstructions(
      cpAmmSdk,
      poolsWithFees,
      halfQuoteTokenFees,
      config.SLIPPAGE,
      wallet.publicKey,
      currentSlot
    );

    // Add swap instructions to batcher
    for (const result of swapResults) {
      batcher.addInstructions(result.instructions, `Swap for ${result.pool.name}`);
    }

    // Step 7: Create liquidity instructions for all pools
    console.log('\n=== STEP 7: MULTI-POOL LIQUIDITY ADDITION ===');

    const liquidityResults = await createMultiPoolLiquidityInstructions(
      cpAmmSdk,
      swapResults,
      wallet.publicKey
    );

    // Add liquidity instructions to batcher
    for (const result of liquidityResults) {
      batcher.addInstructions(result.instructions, `Add liquidity for ${result.pool.name}`);
    }

    // Step 8: Execute all batched transactions
    console.log('\n=== STEP 8: TRANSACTION EXECUTION ===');

    const batchSummary = batcher.getSummary();
    console.log(`Executing ${batchSummary.totalInstructions} instructions across ${batchSummary.estimatedTransactions} transactions...`);

    const txHashes = await batcher.execute();

    // Step 9: Final summary
    console.log('\n=== STEP 9: EXECUTION SUMMARY ===');

    const finalSolBalance = await getSolBalance(solConnection, wallet.publicKey);

    console.log('🎉 Multi-Pool Liquidity Flywheel Completed Successfully!');
    console.log(`\n📊 Summary:`);
    console.log(`  • Pools processed: ${pools.length}`);
    console.log(`  • Pools with fees: ${poolsWithFees.length}`);
    console.log(`  • Transactions executed: ${txHashes.length}`);
    console.log(`  • Initial SOL balance: ${initialSolBalance / 1e9} SOL`);
    console.log(`  • Final SOL balance: ${finalSolBalance / 1e9} SOL`);

    console.log(`\n🔗 Transaction Hashes:`);
    txHashes.forEach((hash, index) => {
      console.log(`  ${index + 1}. ${hash}`);
    });

    console.log(`\n💰 Liquidity Added to Pools:`);
    liquidityResults.forEach(result => {
      console.log(`  • ${result.pool.name}: Liquidity Delta ${result.liquidityDelta.toString()}`);
    });

  } catch (error) {
    console.error('❌ Error in Multi-Pool Liquidity Flywheel:', error);
    console.error('Error details:', error.stack);
    process.exit(1);
  }
}

module.exports = main;
