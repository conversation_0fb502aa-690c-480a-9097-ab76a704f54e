# Slurp - Simple <PERSON><PERSON> Token Script

A barebones Node.js script for interacting with Solana tokens using Meteora's CP-AMM SDK.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Configure your environment variables:
   - Create a `.env` file in the root directory with the following variables:
     ```
     PRIVATE_KEY=your_private_key_here
     RPC_ENDPOINT=https://api.mainnet-beta.solana.com
     SLIPPAGE=0.01
     ```

3. Configure your pools in `src/pools.json`:
   - Update the placeholder addresses with your actual pool and position information:
     ```json
     [
       {
         "name": "TOKEN-SOL",
         "ratio": 34,
         "poolAddress": "your_meteora_pool_address",
         "positionAddress": "your_existing_position_address",
         "positionNftAddress": "your_position_nft_mint_address",
         "positionNftTokenAccount": "your_position_nft_token_account_address",
         "description": "Primary SOL liquidity pool"
       },
       {
         "name": "TOKEN-BTC",
         "ratio": 21,
         "poolAddress": "your_meteora_pool_address",
         "positionAddress": "your_existing_position_address",
         "positionNftAddress": "your_position_nft_mint_address",
         "positionNftTokenAccount": "your_position_nft_token_account_address",
         "description": "Bitcoin wrapped token liquidity pool"
       }
     ]
     ```
   - **Important**: Replace all `REPLACE_WITH_ACTUAL_*` placeholder values with your actual addresses
   - The `ratio` field determines the proportional distribution of fees across pools

## Usage

Run the script:
```
node index.js
```

## Features

The multi-pool liquidity flywheel performs the following operations using Meteora's CP-AMM:

### Core Functionality
- **Multi-Pool Support**: Manages liquidity across multiple token pairs (TOKEN-SOL, TOKEN-BTC, TOKEN-ETH, TOKEN-USDC, TOKEN-EURC)
- **Proportional Fee Distribution**: Distributes fees across pools based on configured ratios
- **Transaction Batching**: Efficiently batches instructions to minimize transaction costs
- **Automated Fee Claiming**: Claims fees from all configured liquidity positions
- **Smart Swapping**: Swaps half of claimed quote tokens back to base tokens for each pool
- **Liquidity Addition**: Adds liquidity back to all positions automatically

### Technical Features
- Initializing the Meteora CP-AMM SDK for multiple pools
- Fetching pool states and position information for all configured pools
- Proportional fee claiming across multiple positions
- Multi-pool swap operations with slippage protection
- Adding liquidity to existing NFT-based positions
- Transaction batching with size optimization
- Comprehensive error handling and logging

## Customization

Modify the `index.js` file to add more functionality as needed. The Meteora CP-AMM SDK provides many more features that you can explore:

- `@meteora-ag/cp-amm-sdk`: Core functionality for concentrated liquidity pools, swaps, and position management
- Position-based liquidity with NFT ownership
- Fee claiming from positions
- Reward claiming mechanisms

## Important Notes

### Meteora CP-AMM vs PumpSwap Differences

This script has been migrated from PumpSwap to Meteora's CP-AMM, which has several key differences:

1. **Position-based Liquidity**: Meteora uses NFT-based positions instead of simple LP tokens
2. **No Creator Vaults**: Meteora doesn't have the same creator vault concept as PumpSwap
3. **Concentrated Liquidity**: Meteora supports concentrated liquidity ranges
4. **Fee Structure**: Different fee claiming mechanisms through positions

### Multi-Pool Configuration Requirements

Make sure your pool configurations are correct:
- Each pool must have valid Meteora CP-AMM addresses
- `poolAddress`: Points to a valid Meteora CP-AMM pool
- `positionAddress`: Points to your existing position in the pool
- `positionNftAddress`: The mint address of your position NFT
- `positionNftTokenAccount`: Your position NFT token account address
- `ratio`: Determines proportional fee distribution (higher ratio = more fees allocated)

You can find available pools on:
- [Meteora App](https://app.meteora.ag/)
- Meteora's documentation and pool listings

**Important**:
- You must already have existing positions in ALL specified pools
- This script adds liquidity to existing positions rather than creating new ones
- All placeholder addresses in `pools.json` must be replaced with actual addresses
- The script will validate all configurations before execution

## Security Notes

- Never hardcode your private key in the script for production use
- Use environment variables or a secure secret management solution
- Be careful when running scripts that interact with real tokens
- Always test with small amounts first
- Understand that position NFTs will be created and managed by the script
